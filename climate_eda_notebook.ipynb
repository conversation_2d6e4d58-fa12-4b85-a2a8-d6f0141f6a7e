{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Climate Data Analysis Project\n", "\n", "This notebook contains comprehensive data preprocessing and exploratory data analysis for the climate simulation dataset.\n", "\n", "**Author:** Data Science Team  \n", "**Date:** 2025-06-27  \n", "**Dataset:** climate.csv  \n", "\n", "## Table of Contents\n", "1. [Data Preprocessing](#section-1)\n", "2. [Exploratory Data Analysis (EDA)](#section-2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 1: Data Preprocessing {#section-1}\n", "\n", "This section handles comprehensive data preprocessing including:\n", "- Data loading and initial exploration\n", "- Missing value detection and handling\n", "- Anomaly detection and treatment\n", "- Feature encoding and transformation\n", "- Train/validation/test dataset splitting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1 Setup and Library Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries for data preprocessing and analysis\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Machine learning libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from sklearn.ensemble import IsolationForest\n", "\n", "# Utility libraries\n", "import pickle\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', None)\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Data Loading and Initial Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the climate dataset\n", "df = pd.read_csv('climate.csv')\n", "\n", "# Identify feature and target columns\n", "target_column = 'outcome'\n", "feature_columns = [col for col in df.columns if col != target_column]\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Features: {len(feature_columns)}\")\n", "print(f\"Target variable: {target_column}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic dataset information\n", "print(\"Dataset Info:\")\n", "print(\"=\" * 50)\n", "df.info()\n", "\n", "print(\"\\nData Types:\")\n", "print(df.dtypes.value_counts())\n", "\n", "print(\"\\nFirst 5 rows:\")\n", "display(df.head())\n", "\n", "print(\"\\nBasic Statistics:\")\n", "display(df.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Missing Values Analysis and Handling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"Missing Values Analysis:\")\n", "print(\"=\" * 50)\n", "\n", "missing_counts = df.isnull().sum()\n", "missing_percentages = (missing_counts / len(df)) * 100\n", "\n", "missing_summary = pd.DataFrame({\n", "    'Missing_Count': missing_counts,\n", "    'Missing_Percentage': missing_percentages\n", "}).sort_values('Missing_Count', ascending=False)\n", "\n", "print(\"Missing Values Summary:\")\n", "missing_with_values = missing_summary[missing_summary['Missing_Count'] > 0]\n", "if len(missing_with_values) > 0:\n", "    display(missing_with_values)\n", "else:\n", "    print(\"✓ No missing values found in the dataset!\")\n", "\n", "# Visualize missing values if any exist\n", "if missing_counts.sum() > 0:\n", "    plt.figure(figsize=(12, 6))\n", "    missing_counts[missing_counts > 0].plot(kind='bar')\n", "    plt.title('Missing Values by Column')\n", "    plt.ylabel('Count')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No missing values visualization needed.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle missing values if any (using median for numerical features)\n", "def handle_missing_values(data, strategy='median'):\n", "    \"\"\"\n", "    Handle missing values using specified strategy\n", "    \"\"\"\n", "    data_cleaned = data.copy()\n", "    \n", "    if missing_counts.sum() == 0:\n", "        print(\"No missing values to handle.\")\n", "        return data_cleaned\n", "    \n", "    print(f\"Handling missing values using '{strategy}' strategy...\")\n", "    \n", "    if strategy == 'median':\n", "        for col in feature_columns:\n", "            if data_cleaned[col].dtype in ['int64', 'float64'] and data_cleaned[col].isnull().sum() > 0:\n", "                median_val = data_cleaned[col].median()\n", "                data_cleaned[col].fillna(median_val, inplace=True)\n", "                print(f\"  Filled {col} with median: {median_val:.4f}\")\n", "    \n", "    elif strategy == 'mean':\n", "        for col in feature_columns:\n", "            if data_cleaned[col].dtype in ['int64', 'float64'] and data_cleaned[col].isnull().sum() > 0:\n", "                mean_val = data_cleaned[col].mean()\n", "                data_cleaned[col].fillna(mean_val, inplace=True)\n", "                print(f\"  Filled {col} with mean: {mean_val:.4f}\")\n", "    \n", "    elif strategy == 'drop':\n", "        data_cleaned = data_cleaned.dropna()\n", "        print(f\"  Dropped rows with missing values. New shape: {data_cleaned.shape}\")\n", "    \n", "    print(\"Missing values handled successfully!\")\n", "    return data_cleaned\n", "\n", "# Apply missing value handling\n", "df_cleaned = handle_missing_values(df, strategy='median')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.4 Anomaly Detection and Treatment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Anomaly detection using Isolation Forest\n", "def detect_anomalies(data, method='isolation_forest', contamination=0.05):\n", "    \"\"\"\n", "    Detect anomalies in the dataset using specified method\n", "    \"\"\"\n", "    print(\"Anomaly Detection:\")\n", "    print(\"=\" * 50)\n", "    \n", "    if method == 'isolation_forest':\n", "        # Use Isolation Forest for anomaly detection\n", "        anomaly_detector = IsolationForest(\n", "            contamination=contamination,\n", "            random_state=42\n", "        )\n", "        \n", "        # Fit on feature columns only\n", "        X = data[feature_columns]\n", "        anomaly_labels = anomaly_detector.fit_predict(X)\n", "        \n", "        # -1 indicates anomaly, 1 indicates normal\n", "        anomaly_mask = anomaly_labels == -1\n", "        n_anomalies = anomaly_mask.sum()\n", "        \n", "        print(f\"Detected {n_anomalies} anomalies ({n_anomalies/len(data)*100:.2f}%) using Isolation Forest\")\n", "        \n", "        # Add anomaly flag to data\n", "        data_with_anomalies = data.copy()\n", "        data_with_anomalies['is_anomaly'] = anomaly_mask\n", "        \n", "        return data_with_anomalies, anomaly_detector\n", "    \n", "    elif method == 'statistical':\n", "        # Z-score based anomaly detection\n", "        z_scores = np.abs(stats.zscore(data[feature_columns]))\n", "        anomaly_mask = (z_scores > 3).any(axis=1)\n", "        n_anomalies = anomaly_mask.sum()\n", "        \n", "        print(f\"Detected {n_anomalies} statistical anomalies ({n_anomalies/len(data)*100:.2f}%) using Z-score > 3\")\n", "        \n", "        data_with_anomalies = data.copy()\n", "        data_with_anomalies['is_anomaly'] = anomaly_mask\n", "        \n", "        return data_with_anomalies, None\n", "\n", "# Apply anomaly detection\n", "df_with_anomalies, anomaly_detector = detect_anomalies(df_cleaned, method='isolation_forest', contamination=0.05)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize anomalies\n", "if 'is_anomaly' in df_with_anomalies.columns:\n", "    anomaly_counts = df_with_anomalies['is_anomaly'].value_counts()\n", "    \n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    # Anomaly distribution\n", "    anomaly_counts.plot(kind='bar', ax=axes[0], color=['lightblue', 'lightcoral'])\n", "    axes[0].set_title('Anomaly Distribution')\n", "    axes[0].set_xlabel('Is Anomaly')\n", "    axes[0].set_ylabel('Count')\n", "    axes[0].set_xticklabels(['Normal', 'Anomaly'], rotation=0)\n", "    \n", "    # Pie chart\n", "    axes[1].pie(anomaly_counts.values, labels=['Normal', 'Anomaly'], autopct='%1.1f%%', \n", "               colors=['lightblue', 'lightcoral'], startangle=90)\n", "    axes[1].set_title('Anomaly Proportion')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Show some anomalous records\n", "    if anomaly_counts[True] > 0:\n", "        print(\"\\nSample of anomalous records:\")\n", "        display(df_with_anomalies[df_with_anomalies['is_anomaly']].head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle anomalies (flag for now, can be changed to remove or cap)\n", "def handle_anomalies(data, action='flag'):\n", "    \"\"\"\n", "    <PERSON><PERSON> detected anomalies\n", "    \"\"\"\n", "    if 'is_anomaly' not in data.columns:\n", "        print(\"No anomalies detected. Skipping anomaly handling.\")\n", "        return data\n", "    \n", "    n_anomalies = data['is_anomaly'].sum()\n", "    \n", "    if action == 'remove':\n", "        data_processed = data[~data['is_anomaly']].copy()\n", "        data_processed = data_processed.drop('is_anomaly', axis=1)\n", "        print(f\"Removed {n_anomalies} anomalous records\")\n", "        \n", "    elif action == 'flag':\n", "        data_processed = data.copy()\n", "        print(f\"Flagged {n_anomalies} anomalous records for further analysis\")\n", "        \n", "    elif action == 'cap':\n", "        data_processed = data.copy()\n", "        # Cap extreme values at 95th percentile\n", "        for col in feature_columns:\n", "            if data_processed[col].dtype in ['int64', 'float64']:\n", "                upper_cap = data_processed[col].quantile(0.95)\n", "                lower_cap = data_processed[col].quantile(0.05)\n", "                data_processed.loc[data_processed['is_anomaly'], col] = np.clip(\n", "                    data_processed.loc[data_processed['is_anomaly'], col], \n", "                    lower_cap, upper_cap\n", "                )\n", "        print(f\"Capped extreme values for {n_anomalies} anomalous records\")\n", "    \n", "    return data_processed\n", "\n", "# Apply anomaly handling (keeping anomalies flagged for now)\n", "df_processed = handle_anomalies(df_with_anomalies, action='flag')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.5 Dataset Splitting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split dataset into train, validation, and test sets\n", "def split_dataset(data, test_size=0.2, val_size=0.2, random_state=42):\n", "    \"\"\"\n", "    Split dataset into train, validation, and test sets\n", "    \"\"\"\n", "    print(\"Dataset Splitting:\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Prepare data for splitting (remove anomaly flag if present)\n", "    data_for_split = data.copy()\n", "    if 'is_anomaly' in data_for_split.columns:\n", "        data_for_split = data_for_split.drop('is_anomaly', axis=1)\n", "    \n", "    X = data_for_split[feature_columns]\n", "    y = data_for_split[target_column]\n", "    \n", "    # First split: separate test set\n", "    X_temp, X_test, y_temp, y_test = train_test_split(\n", "        X, y, test_size=test_size, random_state=random_state, stratify=y\n", "    )\n", "    \n", "    # Second split: separate train and validation sets\n", "    val_size_adjusted = val_size / (1 - test_size)\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X_temp, y_temp, test_size=val_size_adjusted, \n", "        random_state=random_state, stratify=y_temp\n", "    )\n", "    \n", "    print(f\"Dataset split completed:\")\n", "    print(f\"  Training set: {len(X_train)} samples ({len(X_train)/len(X)*100:.1f}%)\")\n", "    print(f\"  Validation set: {len(X_val)} samples ({len(X_val)/len(X)*100:.1f}%)\")\n", "    print(f\"  Test set: {len(X_test)} samples ({len(X_test)/len(X)*100:.1f}%)\")\n", "    \n", "    # Check target distribution in each split\n", "    print(\"\\nTarget distribution in each split:\")\n", "    print(f\"  Training: {y_train.value_counts().to_dict()}\")\n", "    print(f\"  Validation: {y_val.value_counts().to_dict()}\")\n", "    print(f\"  Test: {y_test.value_counts().to_dict()}\")\n", "    \n", "    splits = {\n", "        'X_train': X_train, 'y_train': y_train,\n", "        'X_val': X_val, 'y_val': y_val,\n", "        'X_test': X_test, 'y_test': y_test\n", "    }\n", "    \n", "    return splits\n", "\n", "# Apply dataset splitting\n", "data_splits = split_dataset(df_scaled, test_size=0.2, val_size=0.2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize dataset splits\n", "split_sizes = [len(data_splits['X_train']), len(data_splits['X_val']), len(data_splits['X_test'])]\n", "split_labels = ['Training', 'Validation', 'Test']\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Dataset split sizes\n", "axes[0].pie(split_sizes, labels=split_labels, autopct='%1.1f%%', \n", "           colors=['lightblue', 'lightgreen', 'lightcoral'], startangle=90)\n", "axes[0].set_title('Dataset Split Proportions')\n", "\n", "# Target distribution across splits\n", "train_dist = data_splits['y_train'].value_counts()\n", "val_dist = data_splits['y_val'].value_counts()\n", "test_dist = data_splits['y_test'].value_counts()\n", "\n", "x = np.arange(len(train_dist))\n", "width = 0.25\n", "\n", "axes[1].bar(x - width, train_dist.values, width, label='Training', color='lightblue')\n", "axes[1].bar(x, val_dist.values, width, label='Validation', color='lightgreen')\n", "axes[1].bar(x + width, test_dist.values, width, label='Test', color='lightcoral')\n", "\n", "axes[1].set_xlabel('Target Class')\n", "axes[1].set_ylabel('Count')\n", "axes[1].set_title('Target Distribution Across Splits')\n", "axes[1].set_xticks(x)\n", "axes[1].set_xticklabels(train_dist.index)\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.7 Save Processed Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save processed data and preprocessing objects\n", "def save_processed_data(processed_data, splits, scaler, anomaly_detector, output_prefix='preprocessed_dataset'):\n", "    \"\"\"\n", "    Save processed data in multiple formats\n", "    \"\"\"\n", "    print(\"Saving Processed Data:\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Prepare final dataset (remove anomaly flag for final output)\n", "    final_data = processed_data.copy()\n", "    if 'is_anomaly' in final_data.columns:\n", "        final_data = final_data.drop('is_anomaly', axis=1)\n", "    \n", "    # Save complete processed dataset\n", "    csv_path = f\"{output_prefix}.csv\"\n", "    final_data.to_csv(csv_path, index=False)\n", "    print(f\"✓ Saved processed dataset as {csv_path}\")\n", "    \n", "    # Save as pickle\n", "    pkl_path = f\"{output_prefix}.pkl\"\n", "    with open(pkl_path, 'wb') as f:\n", "        pickle.dump(final_data, f)\n", "    print(f\"✓ Saved processed dataset as {pkl_path}\")\n", "    \n", "    # Save train/val/test splits\n", "    splits_path = f\"{output_prefix}_splits.pkl\"\n", "    with open(splits_path, 'wb') as f:\n", "        pickle.dump(splits, f)\n", "    print(f\"✓ Saved train/val/test splits as {splits_path}\")\n", "    \n", "    # Save preprocessing objects\n", "    preprocessing_objects = {\n", "        'scaler': scaler,\n", "        'anomaly_detector': anomaly_detector,\n", "        'feature_columns': feature_columns,\n", "        'target_column': target_column\n", "    }\n", "    \n", "    objects_path = f\"{output_prefix}_preprocessing_objects.pkl\"\n", "    with open(objects_path, 'wb') as f:\n", "        pickle.dump(preprocessing_objects, f)\n", "    print(f\"✓ Saved preprocessing objects as {objects_path}\")\n", "    \n", "    return {\n", "        'processed_data': csv_path,\n", "        'processed_data_pkl': pkl_path,\n", "        'splits': splits_path,\n", "        'preprocessing_objects': objects_path\n", "    }\n", "\n", "# Save all processed data\n", "output_files = save_processed_data(df_scaled, data_splits, scaler, anomaly_detector)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## Section 2: Exploratory Data Analysis (EDA) {#section-2}\n", "\n", "Now that we have preprocessed the data, let's perform comprehensive exploratory data analysis to understand the patterns, relationships, and characteristics of our climate simulation dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Statistical Description"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive statistical description of processed data\n", "print(\"Comprehensive Statistical Summary (Processed Data):\")\n", "print(\"=\" * 50)\n", "\n", "# Use the final processed data for EDA\n", "eda_data = df_scaled.copy()\n", "if 'is_anomaly' in eda_data.columns:\n", "    eda_data = eda_data.drop('is_anomaly', axis=1)\n", "\n", "# Basic statistics\n", "desc_stats = eda_data.describe()\n", "display(desc_stats)\n", "\n", "# Additional statistics\n", "additional_stats = pd.DataFrame({\n", "    'skewness': eda_data.select_dtypes(include=[np.number]).skew(),\n", "    'kurtosis': eda_data.select_dtypes(include=[np.number]).kurtosis(),\n", "    'variance': eda_data.select_dtypes(include=[np.number]).var()\n", "})\n", "\n", "print(\"\\nAdditional Statistical Measures:\")\n", "display(additional_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Target variable analysis\n", "print(f\"Target Variable Analysis: {target_column}\")\n", "print(\"=\" * 50)\n", "\n", "# Value counts and proportions\n", "target_counts = eda_data[target_column].value_counts().sort_index()\n", "target_props = eda_data[target_column].value_counts(normalize=True).sort_index()\n", "\n", "target_summary = pd.DataFrame({\n", "    'Count': target_counts,\n", "    'Proportion': target_props,\n", "    'Percentage': target_props * 100\n", "})\n", "\n", "display(target_summary)\n", "\n", "print(f\"\\nFeature Variables: {len(feature_columns)} features\")\n", "print(f\"Features: {feature_columns}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Data Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Target variable distribution\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Bar plot\n", "target_counts.plot(kind='bar', ax=axes[0], color='skyblue', alpha=0.8)\n", "axes[0].set_title('Target Variable Distribution (Count)', fontsize=14, fontweight='bold')\n", "axes[0].set_xlabel('Outcome')\n", "axes[0].set_ylabel('Count')\n", "axes[0].tick_params(axis='x', rotation=0)\n", "\n", "# Pie chart\n", "axes[1].pie(target_counts.values, labels=target_counts.index, autopct='%1.1f%%', \n", "           colors=['lightcoral', 'lightblue'], startangle=90)\n", "axes[1].set_title('Target Variable Distribution (Proportion)', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature distributions - Histograms (using processed data)\n", "n_features = len(feature_columns)\n", "n_cols = 4\n", "n_rows = (n_features + n_cols - 1) // n_cols\n", "\n", "fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n", "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n", "\n", "for i, col in enumerate(feature_columns):\n", "    if i < len(axes):\n", "        axes[i].hist(eda_data[col], bins=30, alpha=0.7, color='steelblue', edgecolor='black')\n", "        axes[i].set_title(f'Distribution of {col}', fontsize=12, fontweight='bold')\n", "        axes[i].set_xlabel(col)\n", "        axes[i].set_ylabel('Frequency')\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "# Hide empty subplots\n", "for i in range(len(feature_columns), len(axes)):\n", "    axes[i].set_visible(False)\n", "\n", "plt.suptitle('Feature Distributions (Processed Data)', fontsize=16, fontweight='bold', y=1.02)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Box plots for outlier detection (using processed data)\n", "fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n", "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n", "\n", "for i, col in enumerate(feature_columns):\n", "    if i < len(axes):\n", "        axes[i].boxplot(eda_data[col], patch_artist=True, \n", "                       boxprops=dict(facecolor='lightblue', alpha=0.7))\n", "        axes[i].set_title(f'Box Plot of {col}', fontsize=12, fontweight='bold')\n", "        axes[i].set_ylabel(col)\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "# Hide empty subplots\n", "for i in range(len(feature_columns), len(axes)):\n", "    axes[i].set_visible(False)\n", "\n", "plt.suptitle('Feature Box Plots (Processed Data)', fontsize=16, fontweight='bold', y=1.02)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}