# Import required libraries for data preprocessing and analysis
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Machine learning libraries
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest

# Utility libraries
import pickle
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configure display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

print("Libraries imported successfully!")

# Load the climate dataset
df = pd.read_csv('climate.csv')

# Identify feature and target columns
target_column = 'outcome'
feature_columns = [col for col in df.columns if col != target_column]

print(f"Dataset loaded successfully!")
print(f"Shape: {df.shape}")
print(f"Features: {len(feature_columns)}")
print(f"Target variable: {target_column}")
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Display basic dataset information
print("Dataset Info:")
print("=" * 50)
df.info()

print("\nData Types:")
print(df.dtypes.value_counts())

print("\nFirst 5 rows:")
display(df.head())

print("\nBasic Statistics:")
display(df.describe())

# Check for missing values
print("Missing Values Analysis:")
print("=" * 50)

missing_counts = df.isnull().sum()
missing_percentages = (missing_counts / len(df)) * 100

missing_summary = pd.DataFrame({
    'Missing_Count': missing_counts,
    'Missing_Percentage': missing_percentages
}).sort_values('Missing_Count', ascending=False)

print("Missing Values Summary:")
missing_with_values = missing_summary[missing_summary['Missing_Count'] > 0]
if len(missing_with_values) > 0:
    display(missing_with_values)
else:
    print("✓ No missing values found in the dataset!")

# Visualize missing values if any exist
if missing_counts.sum() > 0:
    plt.figure(figsize=(12, 6))
    missing_counts[missing_counts > 0].plot(kind='bar')
    plt.title('Missing Values by Column')
    plt.ylabel('Count')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
else:
    print("No missing values visualization needed.")

# Handle missing values if any (using median for numerical features)
def handle_missing_values(data, strategy='median'):
    """
    Handle missing values using specified strategy
    """
    data_cleaned = data.copy()
    
    if missing_counts.sum() == 0:
        print("No missing values to handle.")
        return data_cleaned
    
    print(f"Handling missing values using '{strategy}' strategy...")
    
    if strategy == 'median':
        for col in feature_columns:
            if data_cleaned[col].dtype in ['int64', 'float64'] and data_cleaned[col].isnull().sum() > 0:
                median_val = data_cleaned[col].median()
                data_cleaned[col].fillna(median_val, inplace=True)
                print(f"  Filled {col} with median: {median_val:.4f}")
    
    elif strategy == 'mean':
        for col in feature_columns:
            if data_cleaned[col].dtype in ['int64', 'float64'] and data_cleaned[col].isnull().sum() > 0:
                mean_val = data_cleaned[col].mean()
                data_cleaned[col].fillna(mean_val, inplace=True)
                print(f"  Filled {col} with mean: {mean_val:.4f}")
    
    elif strategy == 'drop':
        data_cleaned = data_cleaned.dropna()
        print(f"  Dropped rows with missing values. New shape: {data_cleaned.shape}")
    
    print("Missing values handled successfully!")
    return data_cleaned

# Apply missing value handling
df_cleaned = handle_missing_values(df, strategy='median')

# Anomaly detection using Isolation Forest
def detect_anomalies(data, method='isolation_forest', contamination=0.05):
    """
    Detect anomalies in the dataset using specified method
    """
    print("Anomaly Detection:")
    print("=" * 50)
    
    if method == 'isolation_forest':
        # Use Isolation Forest for anomaly detection
        anomaly_detector = IsolationForest(
            contamination=contamination,
            random_state=42
        )
        
        # Fit on feature columns only
        X = data[feature_columns]
        anomaly_labels = anomaly_detector.fit_predict(X)
        
        # -1 indicates anomaly, 1 indicates normal
        anomaly_mask = anomaly_labels == -1
        n_anomalies = anomaly_mask.sum()
        
        print(f"Detected {n_anomalies} anomalies ({n_anomalies/len(data)*100:.2f}%) using Isolation Forest")
        
        # Add anomaly flag to data
        data_with_anomalies = data.copy()
        data_with_anomalies['is_anomaly'] = anomaly_mask
        
        return data_with_anomalies, anomaly_detector
    
    elif method == 'statistical':
        # Z-score based anomaly detection
        z_scores = np.abs(stats.zscore(data[feature_columns]))
        anomaly_mask = (z_scores > 3).any(axis=1)
        n_anomalies = anomaly_mask.sum()
        
        print(f"Detected {n_anomalies} statistical anomalies ({n_anomalies/len(data)*100:.2f}%) using Z-score > 3")
        
        data_with_anomalies = data.copy()
        data_with_anomalies['is_anomaly'] = anomaly_mask
        
        return data_with_anomalies, None

# Apply anomaly detection
df_with_anomalies, anomaly_detector = detect_anomalies(df_cleaned, method='isolation_forest', contamination=0.05)

# Visualize anomalies
if 'is_anomaly' in df_with_anomalies.columns:
    anomaly_counts = df_with_anomalies['is_anomaly'].value_counts()
    
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    
    # Anomaly distribution
    anomaly_counts.plot(kind='bar', ax=axes[0], color=['lightblue', 'lightcoral'])
    axes[0].set_title('Anomaly Distribution')
    axes[0].set_xlabel('Is Anomaly')
    axes[0].set_ylabel('Count')
    axes[0].set_xticklabels(['Normal', 'Anomaly'], rotation=0)
    
    # Pie chart
    axes[1].pie(anomaly_counts.values, labels=['Normal', 'Anomaly'], autopct='%1.1f%%', 
               colors=['lightblue', 'lightcoral'], startangle=90)
    axes[1].set_title('Anomaly Proportion')
    
    plt.tight_layout()
    plt.show()
    
    # Show some anomalous records
    if anomaly_counts[True] > 0:
        print("\nSample of anomalous records:")
        display(df_with_anomalies[df_with_anomalies['is_anomaly']].head())

# Handle anomalies (flag for now, can be changed to remove or cap)
def handle_anomalies(data, action='flag'):
    """
    Handle detected anomalies
    """
    if 'is_anomaly' not in data.columns:
        print("No anomalies detected. Skipping anomaly handling.")
        return data
    
    n_anomalies = data['is_anomaly'].sum()
    
    if action == 'remove':
        data_processed = data[~data['is_anomaly']].copy()
        data_processed = data_processed.drop('is_anomaly', axis=1)
        print(f"Removed {n_anomalies} anomalous records")
        
    elif action == 'flag':
        data_processed = data.copy()
        print(f"Flagged {n_anomalies} anomalous records for further analysis")
        
    elif action == 'cap':
        data_processed = data.copy()
        # Cap extreme values at 95th percentile
        for col in feature_columns:
            if data_processed[col].dtype in ['int64', 'float64']:
                upper_cap = data_processed[col].quantile(0.95)
                lower_cap = data_processed[col].quantile(0.05)
                data_processed.loc[data_processed['is_anomaly'], col] = np.clip(
                    data_processed.loc[data_processed['is_anomaly'], col], 
                    lower_cap, upper_cap
                )
        print(f"Capped extreme values for {n_anomalies} anomalous records")
    
    return data_processed

# Apply anomaly handling (keeping anomalies flagged for now)
df_processed = handle_anomalies(df_with_anomalies, action='flag')

# Split dataset into train, validation, and test sets
def split_dataset(data, test_size=0.2, val_size=0.2, random_state=42):
    """
    Split dataset into train, validation, and test sets
    """
    print("Dataset Splitting:")
    print("=" * 50)
    
    # Prepare data for splitting (remove anomaly flag if present)
    data_for_split = data.copy()
    if 'is_anomaly' in data_for_split.columns:
        data_for_split = data_for_split.drop('is_anomaly', axis=1)
    
    X = data_for_split[feature_columns]
    y = data_for_split[target_column]
    
    # First split: separate test set
    X_temp, X_test, y_temp, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Second split: separate train and validation sets
    val_size_adjusted = val_size / (1 - test_size)
    X_train, X_val, y_train, y_val = train_test_split(
        X_temp, y_temp, test_size=val_size_adjusted, 
        random_state=random_state, stratify=y_temp
    )
    
    print(f"Dataset split completed:")
    print(f"  Training set: {len(X_train)} samples ({len(X_train)/len(X)*100:.1f}%)")
    print(f"  Validation set: {len(X_val)} samples ({len(X_val)/len(X)*100:.1f}%)")
    print(f"  Test set: {len(X_test)} samples ({len(X_test)/len(X)*100:.1f}%)")
    
    # Check target distribution in each split
    print("\nTarget distribution in each split:")
    print(f"  Training: {y_train.value_counts().to_dict()}")
    print(f"  Validation: {y_val.value_counts().to_dict()}")
    print(f"  Test: {y_test.value_counts().to_dict()}")
    
    splits = {
        'X_train': X_train, 'y_train': y_train,
        'X_val': X_val, 'y_val': y_val,
        'X_test': X_test, 'y_test': y_test
    }
    
    return splits

# Apply dataset splitting
data_splits = split_dataset(df_scaled, test_size=0.2, val_size=0.2) # type: ignore

# Visualize dataset splits
split_sizes = [len(data_splits['X_train']), len(data_splits['X_val']), len(data_splits['X_test'])]
split_labels = ['Training', 'Validation', 'Test']

fig, axes = plt.subplots(1, 2, figsize=(15, 5))

# Dataset split sizes
axes[0].pie(split_sizes, labels=split_labels, autopct='%1.1f%%', 
           colors=['lightblue', 'lightgreen', 'lightcoral'], startangle=90)
axes[0].set_title('Dataset Split Proportions')

# Target distribution across splits
train_dist = data_splits['y_train'].value_counts()
val_dist = data_splits['y_val'].value_counts()
test_dist = data_splits['y_test'].value_counts()

x = np.arange(len(train_dist))
width = 0.25

axes[1].bar(x - width, train_dist.values, width, label='Training', color='lightblue')
axes[1].bar(x, val_dist.values, width, label='Validation', color='lightgreen')
axes[1].bar(x + width, test_dist.values, width, label='Test', color='lightcoral')

axes[1].set_xlabel('Target Class')
axes[1].set_ylabel('Count')
axes[1].set_title('Target Distribution Across Splits')
axes[1].set_xticks(x)
axes[1].set_xticklabels(train_dist.index)
axes[1].legend()
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Save processed data and preprocessing objects
def save_processed_data(processed_data, splits, scaler, anomaly_detector, output_prefix='preprocessed_dataset'):
    """
    Save processed data in multiple formats
    """
    print("Saving Processed Data:")
    print("=" * 50)
    
    # Prepare final dataset (remove anomaly flag for final output)
    final_data = processed_data.copy()
    if 'is_anomaly' in final_data.columns:
        final_data = final_data.drop('is_anomaly', axis=1)
    
    # Save complete processed dataset
    csv_path = f"{output_prefix}.csv"
    final_data.to_csv(csv_path, index=False)
    print(f"✓ Saved processed dataset as {csv_path}")
    
    # Save as pickle
    pkl_path = f"{output_prefix}.pkl"
    with open(pkl_path, 'wb') as f:
        pickle.dump(final_data, f)
    print(f"✓ Saved processed dataset as {pkl_path}")
    
    # Save train/val/test splits
    splits_path = f"{output_prefix}_splits.pkl"
    with open(splits_path, 'wb') as f:
        pickle.dump(splits, f)
    print(f"✓ Saved train/val/test splits as {splits_path}")
    
    # Save preprocessing objects
    preprocessing_objects = {
        'scaler': scaler,
        'anomaly_detector': anomaly_detector,
        'feature_columns': feature_columns,
        'target_column': target_column
    }
    
    objects_path = f"{output_prefix}_preprocessing_objects.pkl"
    with open(objects_path, 'wb') as f:
        pickle.dump(preprocessing_objects, f)
    print(f"✓ Saved preprocessing objects as {objects_path}")
    
    return {
        'processed_data': csv_path,
        'processed_data_pkl': pkl_path,
        'splits': splits_path,
        'preprocessing_objects': objects_path
    }

# Save all processed data
output_files = save_processed_data(df_scaled, data_splits, scaler, anomaly_detector)

# Comprehensive statistical description of processed data
print("Comprehensive Statistical Summary (Processed Data):")
print("=" * 50)

# Use the final processed data for EDA
eda_data = df_scaled.copy()
if 'is_anomaly' in eda_data.columns:
    eda_data = eda_data.drop('is_anomaly', axis=1)

# Basic statistics
desc_stats = eda_data.describe()
display(desc_stats)

# Additional statistics
additional_stats = pd.DataFrame({
    'skewness': eda_data.select_dtypes(include=[np.number]).skew(),
    'kurtosis': eda_data.select_dtypes(include=[np.number]).kurtosis(),
    'variance': eda_data.select_dtypes(include=[np.number]).var()
})

print("\nAdditional Statistical Measures:")
display(additional_stats)

# Target variable analysis
print(f"Target Variable Analysis: {target_column}")
print("=" * 50)

# Value counts and proportions
target_counts = eda_data[target_column].value_counts().sort_index()
target_props = eda_data[target_column].value_counts(normalize=True).sort_index()

target_summary = pd.DataFrame({
    'Count': target_counts,
    'Proportion': target_props,
    'Percentage': target_props * 100
})

display(target_summary)

print(f"\nFeature Variables: {len(feature_columns)} features")
print(f"Features: {feature_columns}")

# Target variable distribution
fig, axes = plt.subplots(1, 2, figsize=(15, 5))

# Bar plot
target_counts.plot(kind='bar', ax=axes[0], color='skyblue', alpha=0.8)
axes[0].set_title('Target Variable Distribution (Count)', fontsize=14, fontweight='bold')
axes[0].set_xlabel('Outcome')
axes[0].set_ylabel('Count')
axes[0].tick_params(axis='x', rotation=0)

# Pie chart
axes[1].pie(target_counts.values, labels=target_counts.index, autopct='%1.1f%%', 
           colors=['lightcoral', 'lightblue'], startangle=90)
axes[1].set_title('Target Variable Distribution (Proportion)', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.show()

# Feature distributions - Histograms (using processed data)
n_features = len(feature_columns)
n_cols = 4
n_rows = (n_features + n_cols - 1) // n_cols

fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes

for i, col in enumerate(feature_columns):
    if i < len(axes):
        axes[i].hist(eda_data[col], bins=30, alpha=0.7, color='steelblue', edgecolor='black')
        axes[i].set_title(f'Distribution of {col}', fontsize=12, fontweight='bold')
        axes[i].set_xlabel(col)
        axes[i].set_ylabel('Frequency')
        axes[i].grid(True, alpha=0.3)

# Hide empty subplots
for i in range(len(feature_columns), len(axes)):
    axes[i].set_visible(False)

plt.suptitle('Feature Distributions (Processed Data)', fontsize=16, fontweight='bold', y=1.02)
plt.tight_layout()
plt.show()

# Box plots for outlier detection (using processed data)
fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes

for i, col in enumerate(feature_columns):
    if i < len(axes):
        axes[i].boxplot(eda_data[col], patch_artist=True, 
                       boxprops=dict(facecolor='lightblue', alpha=0.7))
        axes[i].set_title(f'Box Plot of {col}', fontsize=12, fontweight='bold')
        axes[i].set_ylabel(col)
        axes[i].grid(True, alpha=0.3)

# Hide empty subplots
for i in range(len(feature_columns), len(axes)):
    axes[i].set_visible(False)

plt.suptitle('Feature Box Plots (Processed Data)', fontsize=16, fontweight='bold', y=1.02)
plt.tight_layout()
plt.show()