"""
Climate Data Preprocessing Pipeline
===================================

This script handles comprehensive data preprocessing for climate simulation data including:
- Data loading and initial exploration
- Missing value detection and handling
- Anomaly detection and treatment
- Feature encoding and transformation
- Train/validation/test dataset splitting
- Export processed data in multiple formats

Author: Data Science Team
Date: 2025-06-27
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest
from scipy import stats
import pickle
import warnings
warnings.filterwarnings('ignore')

class ClimateDataPreprocessor:
    """
    Comprehensive data preprocessing pipeline for climate simulation data
    """
    
    def __init__(self, file_path='climate.csv', random_state=42):
        """
        Initialize the preprocessor
        
        Args:
            file_path (str): Path to the climate data CSV file
            random_state (int): Random state for reproducibility
        """
        self.file_path = file_path
        self.random_state = random_state
        self.data = None
        self.processed_data = None
        self.feature_columns = None
        self.target_column = 'outcome'
        self.scaler = None
        self.anomaly_detector = None
        
    def load_data(self, sample_size=None):
        """
        Load climate data from CSV file
        
        Args:
            sample_size (int, optional): Number of rows to sample for initial analysis
        """
        print("Loading climate data...")
        self.data = pd.read_csv(self.file_path)
        
        if sample_size and sample_size < len(self.data):
            print(f"Sampling {sample_size} rows from {len(self.data)} total rows")
            self.data = self.data.sample(n=sample_size, random_state=self.random_state)
        
        # Identify feature columns (all except target)
        self.feature_columns = [col for col in self.data.columns if col != self.target_column]
        
        print(f"Data loaded successfully!")
        print(f"Shape: {self.data.shape}")
        print(f"Features: {len(self.feature_columns)}")
        print(f"Target variable: {self.target_column}")
        
        return self.data
    
    def explore_data_structure(self):
        """
        Perform initial data exploration
        """
        print("\n" + "="*50)
        print("DATA STRUCTURE EXPLORATION")
        print("="*50)
        
        print("\nDataset Info:")
        print(f"Shape: {self.data.shape}")
        print(f"Memory usage: {self.data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        print("\nData Types:")
        print(self.data.dtypes.value_counts())
        
        print("\nFirst 5 rows:")
        print(self.data.head())
        
        print("\nBasic Statistics:")
        print(self.data.describe())
        
        return self.data.info()
    
    def detect_missing_values(self):
        """
        Detect and analyze missing values
        """
        print("\n" + "="*50)
        print("MISSING VALUES ANALYSIS")
        print("="*50)
        
        missing_counts = self.data.isnull().sum()
        missing_percentages = (missing_counts / len(self.data)) * 100
        
        missing_summary = pd.DataFrame({
            'Missing_Count': missing_counts,
            'Missing_Percentage': missing_percentages
        }).sort_values('Missing_Count', ascending=False)
        
        print("\nMissing Values Summary:")
        print(missing_summary[missing_summary['Missing_Count'] > 0])
        
        if missing_summary['Missing_Count'].sum() == 0:
            print("✓ No missing values detected!")
        
        return missing_summary
    
    def handle_missing_values(self, strategy='median'):
        """
        Handle missing values using specified strategy
        
        Args:
            strategy (str): Strategy for handling missing values
                          'median', 'mean', 'mode', 'drop'
        """
        print(f"\nHandling missing values using '{strategy}' strategy...")
        
        if strategy == 'drop':
            self.data = self.data.dropna()
        elif strategy == 'median':
            for col in self.feature_columns:
                if self.data[col].dtype in ['int64', 'float64']:
                    self.data[col].fillna(self.data[col].median(), inplace=True)
        elif strategy == 'mean':
            for col in self.feature_columns:
                if self.data[col].dtype in ['int64', 'float64']:
                    self.data[col].fillna(self.data[col].mean(), inplace=True)
        
        print("Missing values handled successfully!")
        return self.data
    
    def detect_anomalies(self, method='isolation_forest', contamination=0.1):
        """
        Detect anomalies in the dataset
        
        Args:
            method (str): Anomaly detection method
            contamination (float): Expected proportion of anomalies
        """
        print("\n" + "="*50)
        print("ANOMALY DETECTION")
        print("="*50)
        
        if method == 'isolation_forest':
            self.anomaly_detector = IsolationForest(
                contamination=contamination,
                random_state=self.random_state
            )
            
            # Fit on feature columns only
            X = self.data[self.feature_columns]
            anomaly_labels = self.anomaly_detector.fit_predict(X)
            
            # -1 indicates anomaly, 1 indicates normal
            anomaly_mask = anomaly_labels == -1
            n_anomalies = anomaly_mask.sum()
            
            print(f"Detected {n_anomalies} anomalies ({n_anomalies/len(self.data)*100:.2f}%)")
            
            # Add anomaly flag to data
            self.data['is_anomaly'] = anomaly_mask
            
        elif method == 'statistical':
            # Z-score based anomaly detection
            z_scores = np.abs(stats.zscore(self.data[self.feature_columns]))
            anomaly_mask = (z_scores > 3).any(axis=1)
            n_anomalies = anomaly_mask.sum()
            
            print(f"Detected {n_anomalies} statistical anomalies ({n_anomalies/len(self.data)*100:.2f}%)")
            self.data['is_anomaly'] = anomaly_mask
        
        return anomaly_mask
    
    def handle_anomalies(self, action='flag'):
        """
        Handle detected anomalies
        
        Args:
            action (str): Action to take with anomalies ('remove', 'flag', 'cap')
        """
        if 'is_anomaly' not in self.data.columns:
            print("No anomalies detected. Run detect_anomalies() first.")
            return
        
        n_anomalies = self.data['is_anomaly'].sum()
        
        if action == 'remove':
            self.data = self.data[~self.data['is_anomaly']]
            print(f"Removed {n_anomalies} anomalous records")
        elif action == 'flag':
            print(f"Flagged {n_anomalies} anomalous records for further analysis")
        elif action == 'cap':
            # Cap extreme values at 95th percentile
            for col in self.feature_columns:
                if self.data[col].dtype in ['int64', 'float64']:
                    upper_cap = self.data[col].quantile(0.95)
                    lower_cap = self.data[col].quantile(0.05)
                    self.data.loc[self.data['is_anomaly'], col] = np.clip(
                        self.data.loc[self.data['is_anomaly'], col], 
                        lower_cap, upper_cap
                    )
            print(f"Capped extreme values for {n_anomalies} anomalous records")
        
        return self.data
    
    def encode_features(self, scaling_method='standard'):
        """
        Encode and scale features
        
        Args:
            scaling_method (str): Scaling method ('standard', 'robust', 'none')
        """
        print("\n" + "="*50)
        print("FEATURE ENCODING AND SCALING")
        print("="*50)
        
        # Create a copy for processing
        self.processed_data = self.data.copy()
        
        # Remove anomaly flag if it exists
        if 'is_anomaly' in self.processed_data.columns:
            self.processed_data = self.processed_data.drop('is_anomaly', axis=1)
        
        if scaling_method == 'standard':
            self.scaler = StandardScaler()
            self.processed_data[self.feature_columns] = self.scaler.fit_transform(
                self.processed_data[self.feature_columns]
            )
            print("Applied Standard Scaling to features")
            
        elif scaling_method == 'robust':
            self.scaler = RobustScaler()
            self.processed_data[self.feature_columns] = self.scaler.fit_transform(
                self.processed_data[self.feature_columns]
            )
            print("Applied Robust Scaling to features")
            
        elif scaling_method == 'none':
            print("No scaling applied")
        
        return self.processed_data
    
    def split_dataset(self, test_size=0.2, val_size=0.2):
        """
        Split dataset into train, validation, and test sets
        
        Args:
            test_size (float): Proportion of data for test set
            val_size (float): Proportion of remaining data for validation set
        """
        print("\n" + "="*50)
        print("DATASET SPLITTING")
        print("="*50)
        
        if self.processed_data is None:
            self.processed_data = self.data.copy()
            if 'is_anomaly' in self.processed_data.columns:
                self.processed_data = self.processed_data.drop('is_anomaly', axis=1)
        
        X = self.processed_data[self.feature_columns]
        y = self.processed_data[self.target_column]
        
        # First split: separate test set
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=self.random_state, stratify=y
        )
        
        # Second split: separate train and validation sets
        val_size_adjusted = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, 
            random_state=self.random_state, stratify=y_temp
        )
        
        print(f"Dataset split completed:")
        print(f"  Training set: {len(X_train)} samples ({len(X_train)/len(X)*100:.1f}%)")
        print(f"  Validation set: {len(X_val)} samples ({len(X_val)/len(X)*100:.1f}%)")
        print(f"  Test set: {len(X_test)} samples ({len(X_test)/len(X)*100:.1f}%)")
        
        # Store splits
        self.splits = {
            'X_train': X_train, 'y_train': y_train,
            'X_val': X_val, 'y_val': y_val,
            'X_test': X_test, 'y_test': y_test
        }
        
        return self.splits
    
    def save_processed_data(self, output_prefix='preprocessed_dataset'):
        """
        Save processed data in multiple formats
        
        Args:
            output_prefix (str): Prefix for output files
        """
        print("\n" + "="*50)
        print("SAVING PROCESSED DATA")
        print("="*50)
        
        # Save complete processed dataset
        if self.processed_data is not None:
            # Save as CSV
            csv_path = f"{output_prefix}.csv"
            self.processed_data.to_csv(csv_path, index=False)
            print(f"✓ Saved processed dataset as {csv_path}")
            
            # Save as pickle
            pkl_path = f"{output_prefix}.pkl"
            with open(pkl_path, 'wb') as f:
                pickle.dump(self.processed_data, f)
            print(f"✓ Saved processed dataset as {pkl_path}")
        
        # Save train/val/test splits if available
        if hasattr(self, 'splits'):
            splits_path = f"{output_prefix}_splits.pkl"
            with open(splits_path, 'wb') as f:
                pickle.dump(self.splits, f)
            print(f"✓ Saved train/val/test splits as {splits_path}")
        
        # Save preprocessing objects
        preprocessing_objects = {
            'scaler': self.scaler,
            'anomaly_detector': self.anomaly_detector,
            'feature_columns': self.feature_columns,
            'target_column': self.target_column
        }
        
        objects_path = f"{output_prefix}_preprocessing_objects.pkl"
        with open(objects_path, 'wb') as f:
            pickle.dump(preprocessing_objects, f)
        print(f"✓ Saved preprocessing objects as {objects_path}")
        
        return {
            'processed_data': f"{output_prefix}.csv",
            'processed_data_pkl': f"{output_prefix}.pkl",
            'splits': f"{output_prefix}_splits.pkl",
            'preprocessing_objects': f"{output_prefix}_preprocessing_objects.pkl"
        }

def main():
    """
    Main preprocessing pipeline execution
    """
    print("CLIMATE DATA PREPROCESSING PIPELINE")
    print("="*60)
    
    # Initialize preprocessor
    preprocessor = ClimateDataPreprocessor('climate.csv')
    
    # Load data (sample for initial analysis if dataset is large)
    data = preprocessor.load_data()
    
    # Explore data structure
    preprocessor.explore_data_structure()
    
    # Analyze missing values
    missing_summary = preprocessor.detect_missing_values()
    
    # Handle missing values if any
    if missing_summary['Missing_Count'].sum() > 0:
        preprocessor.handle_missing_values(strategy='median')
    
    # Detect anomalies
    preprocessor.detect_anomalies(method='isolation_forest', contamination=0.05)
    
    # Handle anomalies (flag for now, can be changed to 'remove' or 'cap')
    preprocessor.handle_anomalies(action='flag')
    
    # Encode and scale features
    preprocessor.encode_features(scaling_method='standard')
    
    # Split dataset
    splits = preprocessor.split_dataset(test_size=0.2, val_size=0.2)
    
    # Save processed data
    output_files = preprocessor.save_processed_data()
    
    print("\n" + "="*60)
    print("PREPROCESSING COMPLETED SUCCESSFULLY!")
    print("="*60)
    print("Output files:")
    for key, path in output_files.items():
        print(f"  {key}: {path}")
    
    return preprocessor

if __name__ == "__main__":
    preprocessor = main()
