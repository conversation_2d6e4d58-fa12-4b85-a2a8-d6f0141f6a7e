# 创建 df_scaled 变量的代码
# 请将此代码复制到 Jupyter notebook 的新单元格中运行

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

print("Creating df_scaled variable...")
print("=" * 50)

# 检查必要的变量是否存在
required_vars = ['df', 'feature_columns', 'target_column']
missing_vars = []

# 模拟检查（在notebook中这些变量应该已经存在）
try:
    # 如果在notebook中运行，这些变量应该已经定义
    # 这里我们假设它们存在，如果不存在会在运行时报错
    
    # 加载数据（如果df不存在）
    try:
        df_test = df
        print("✓ df variable exists")
    except NameError:
        print("Loading data from climate.csv...")
        df = pd.read_csv('climate.csv')
        print("✓ df loaded successfully")
    
    # 定义列名（如果不存在）
    try:
        target_test = target_column
        features_test = feature_columns
        print("✓ target_column and feature_columns exist")
    except NameError:
        print("Defining target_column and feature_columns...")
        target_column = 'outcome'
        feature_columns = [col for col in df.columns if col != target_column]
        print(f"✓ target_column: {target_column}")
        print(f"✓ feature_columns: {len(feature_columns)} features")
    
    # 检查是否存在 df_processed，如果不存在则使用 df
    try:
        data_to_scale = df_processed.copy()
        print("✓ Using df_processed for scaling")
    except NameError:
        try:
            data_to_scale = df_cleaned.copy()
            print("✓ Using df_cleaned for scaling")
        except NameError:
            data_to_scale = df.copy()
            print("✓ Using df for scaling")
    
    # 移除异常标记列（如果存在）
    if 'is_anomaly' in data_to_scale.columns:
        print("Removing 'is_anomaly' column for scaling...")
        anomaly_flag = data_to_scale['is_anomaly'].copy()
        data_to_scale = data_to_scale.drop('is_anomaly', axis=1)
    else:
        anomaly_flag = None
        print("No 'is_anomaly' column found")
    
    # 分离特征和目标变量
    X = data_to_scale[feature_columns]
    y = data_to_scale[target_column]
    
    print(f"Data shape for scaling: {X.shape}")
    print(f"Target shape: {y.shape}")
    
    # 创建并应用StandardScaler
    print("\nApplying StandardScaler...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 创建缩放后的DataFrame
    X_scaled_df = pd.DataFrame(X_scaled, columns=feature_columns, index=X.index)
    
    # 合并缩放后的特征和目标变量
    df_scaled = pd.concat([X_scaled_df, y], axis=1)
    
    # 如果存在异常标记，重新添加
    if anomaly_flag is not None:
        df_scaled['is_anomaly'] = anomaly_flag
        print("Re-added 'is_anomaly' column")
    
    print(f"\n✓ df_scaled created successfully!")
    print(f"Shape: {df_scaled.shape}")
    print(f"Columns: {list(df_scaled.columns)}")
    
    # 显示缩放统计信息
    print(f"\nScaling Statistics:")
    print(f"  Mean of scaled features: {X_scaled_df.mean().mean():.6f}")
    print(f"  Std of scaled features: {X_scaled_df.std().mean():.6f}")
    
    # 显示前几行
    print(f"\nFirst 3 rows of df_scaled:")
    print(df_scaled.head(3))
    
    print(f"\n🎉 df_scaled variable is now ready to use!")
    
except Exception as e:
    print(f"❌ Error creating df_scaled: {str(e)}")
    print("\nPlease make sure you have run the previous cells to create:")
    print("- df (original dataset)")
    print("- feature_columns (list of feature column names)")
    print("- target_column (target variable name)")
