# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import plotly.express as px
import plotly.graph_objects as go

# Machine learning libraries
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest

# Utility libraries
import pickle
import warnings
import os
from datetime import datetime

# Configure settings
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.precision', 4)
plt.style.use('default')
sns.set_palette("husl")



# Load the climate dataset
print("🔄 Loading climate dataset...")
print("=" * 50)

# Check if file exists
if not os.path.exists('climate.csv'):
    raise FileNotFoundError("❌ climate.csv file not found in current directory")

# Load data
df_raw = pd.read_csv('climate.csv')
df = df_raw.copy()  # Working copy

# Define columns
target_column = 'outcome'
feature_columns = [col for col in df.columns if col != target_column]

# Basic information


# Display basic info

for i, col in enumerate(feature_columns, 1):
    print(f"  {i:2d}. {col}")

print(f"\n🔍 First 3 rows:")
display(df.head(3))

# Data quality assessment
print("🔍 Data Quality Assessment:")
print("=" * 50)

# Missing values
missing_counts = df.isnull().sum()
total_missing = missing_counts.sum()
print(f" Missing Values: {total_missing:,} ({(total_missing/(len(df)*len(df.columns)))*100:.2f}%)")

if total_missing > 0:
    missing_summary = pd.DataFrame({
        'Missing_Count': missing_counts[missing_counts > 0],
        'Missing_Percentage': (missing_counts[missing_counts > 0] / len(df)) * 100
    })
    display(missing_summary)
else:
    print("✅ No missing values found")

# Duplicates
duplicate_rows = df.duplicated().sum()
print(f"\n🔄 Duplicate Rows: {duplicate_rows:,} ({(duplicate_rows/len(df))*100:.2f}%)")

# Data types
print(f"\n📈 Data Types:")
for dtype, count in df.dtypes.value_counts().items():
    print(f"  {dtype}: {count} columns")

# Target distribution

target_dist = df[target_column].value_counts().sort_index()
for value, count in target_dist.items():
    pct = (count / len(df)) * 100
    print(f"  Class {value}: {count:,} ({pct:.1f}%)")

# Check for class imbalance
imbalance_ratio = target_dist.max() / target_dist.min()

if imbalance_ratio > 2:
    print(" Dataset shows class imbalance")
else:
    print(" Dataset is relatively balanced")

# Data cleaning
print("🧹 Data Cleaning:")
print("=" * 50)

df_cleaned = df.copy()
initial_shape = df_cleaned.shape

# Handle missing values (if any)
if total_missing > 0:

    for col in feature_columns:
        if df_cleaned[col].isnull().sum() > 0:
            median_val = df_cleaned[col].median()
            df_cleaned[col].fillna(median_val, inplace=True)
            print(f"   {col}: filled with median {median_val:.4f}")
else:
    print(" No missing values to handle")

# Handle duplicates
if duplicate_rows > 0:
    df_cleaned = df_cleaned.drop_duplicates()
    print(f"   Duplicates removed")
else:
    print(" No duplicates to remove")

# Summary
final_shape = df_cleaned.shape
print(f"\n Cleaning Summary:")
print(f"  Initial: {initial_shape[0]:,} × {initial_shape[1]}")
print(f"  Final: {final_shape[0]:,} × {final_shape[1]}")
print(f"  Retention: {(final_shape[0]/initial_shape[0])*100:.1f}%")

print(f"\n Data cleaning completed!")

# Anomaly detection
print("🔍 Anomaly Detection:")
print("=" * 50)

# Use Isolation Forest for anomaly detection

anomaly_detector = IsolationForest(
    contamination=0.05,  # Expect 5% anomalies
    random_state=42,
    n_estimators=100
)

# Fit on features only
X_features = df_cleaned[feature_columns]
anomaly_labels = anomaly_detector.fit_predict(X_features)

# Add anomaly flag (-1 = anomaly, 1 = normal)
df_cleaned['is_anomaly'] = (anomaly_labels == -1)
n_anomalies = df_cleaned['is_anomaly'].sum()


print(f"  Anomalies detected: {n_anomalies:,} ({(n_anomalies/len(df_cleaned))*100:.2f}%)")
print(f"  Normal samples: {len(df_cleaned) - n_anomalies:,}")

# Visualize anomalies
if n_anomalies > 0:
    fig, axes = plt.subplots(1, 2, figsize=(12, 4))
    
    # Count plot
    anomaly_counts = df_cleaned['is_anomaly'].value_counts()
    anomaly_counts.plot(kind='bar', ax=axes[0], color=['lightblue', 'coral'])
    axes[0].set_title('Anomaly Distribution')
    axes[0].set_xlabel('Is Anomaly')
    axes[0].set_ylabel('Count')
    axes[0].set_xticklabels(['Normal', 'Anomaly'], rotation=0)
    
    # Pie chart
    axes[1].pie(anomaly_counts.values, labels=['Normal', 'Anomaly'], 
               autopct='%1.1f%%', colors=['lightblue', 'coral'])
    axes[1].set_title('Anomaly Proportion')
    
    plt.tight_layout()
    plt.show()
    
    print(f"\n🔍 Sample anomalous records:")
    display(df_cleaned[df_cleaned['is_anomaly']].head(3))

print(f"\n Anomaly detection completed!")

# Feature scaling
print("⚙️ Feature Engineering - Scaling:")
print("=" * 50)

# Prepare data for scaling (remove anomaly flag temporarily)
data_for_scaling = df_cleaned.copy()
anomaly_flag = data_for_scaling['is_anomaly'].copy()
data_for_scaling = data_for_scaling.drop('is_anomaly', axis=1)

# Separate features and target
X = data_for_scaling[feature_columns]
y = data_for_scaling[target_column]


# Apply StandardScaler
print(f"\n🔧 Applying StandardScaler...")
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_scaled_df = pd.DataFrame(X_scaled, columns=feature_columns, index=X.index)

# Create final scaled dataset
df_scaled = pd.concat([X_scaled_df, y], axis=1)
df_scaled['is_anomaly'] = anomaly_flag  # Add back anomaly flag


display(df_scaled.head(3))

# Dataset splitting
print("📊 Dataset Splitting:")
print("=" * 50)

# Prepare data for splitting (remove anomaly flag)
data_for_split = df_scaled.drop('is_anomaly', axis=1)
X_all = data_for_split[feature_columns]
y_all = data_for_split[target_column]

print(f"\n📊 Data for splitting:")
print(f"  Total samples: {len(X_all):,}")
print(f"  Features: {len(feature_columns)}")
print(f"  Target classes: {sorted(y_all.unique())}")

# Split into train, validation, and test sets
# First split: separate test set (20%)
X_temp, X_test, y_temp, y_test = train_test_split(
    X_all, y_all, test_size=0.2, random_state=42, stratify=y_all
)

# Second split: separate train and validation sets (60% train, 20% val)
X_train, X_val, y_train, y_val = train_test_split(
    X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp
)

# Create splits dictionary
data_splits = {
    'X_train': X_train, 'y_train': y_train,
    'X_val': X_val, 'y_val': y_val,
    'X_test': X_test, 'y_test': y_test
}

# Display split information
print(f"\n📊 Split Results:")
print(f"  Training set: {len(X_train):,} samples ({len(X_train)/len(X_all)*100:.1f}%)")
print(f"  Validation set: {len(X_val):,} samples ({len(X_val)/len(X_all)*100:.1f}%)")
print(f"  Test set: {len(X_test):,} samples ({len(X_test)/len(X_all)*100:.1f}%)")

# Check target distribution in each split
print(f"\n🎯 Target Distribution:")
for split_name, y_split in [('Train', y_train), ('Validation', y_val), ('Test', y_test)]:
    dist = y_split.value_counts().sort_index()
    dist_str = ', '.join([f"Class {k}: {v} ({v/len(y_split)*100:.1f}%)" for k, v in dist.items()])
    print(f"  {split_name}: {dist_str}")

print(f"\n✅ Dataset splitting completed!")

# Visualize dataset splits
print("📊 Split Visualization:")
print("=" * 30)

fig, axes = plt.subplots(1, 2, figsize=(12, 4))

# Split sizes pie chart
split_sizes = [len(X_train), len(X_val), len(X_test)]
split_labels = ['Training (60%)', 'Validation (20%)', 'Test (20%)']
colors = ['lightblue', 'lightgreen', 'lightcoral']

axes[0].pie(split_sizes, labels=split_labels, autopct='%1.1f%%', 
           colors=colors, startangle=90)
axes[0].set_title('Dataset Split Proportions')

# Target distribution across splits
train_dist = y_train.value_counts().sort_index()
val_dist = y_val.value_counts().sort_index()
test_dist = y_test.value_counts().sort_index()

x = np.arange(len(train_dist))
width = 0.25

axes[1].bar(x - width, train_dist.values, width, label='Training', color='lightblue')
axes[1].bar(x, val_dist.values, width, label='Validation', color='lightgreen')
axes[1].bar(x + width, test_dist.values, width, label='Test', color='lightcoral')

axes[1].set_xlabel('Target Class')
axes[1].set_ylabel('Count')
axes[1].set_title('Target Distribution Across Splits')
axes[1].set_xticks(x)
axes[1].set_xticklabels(train_dist.index)
axes[1].legend()
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Save processed data and objects
print(" Saving Processed Data:")
print("=" * 50)

# Prepare final dataset (without anomaly flag)
final_data = df_scaled.drop('is_anomaly', axis=1)

# Save processed dataset
output_files = {}

# 1. Save as CSV
csv_path = 'preprocessed_dataset.csv'
final_data.to_csv(csv_path, index=False)
output_files['csv'] = csv_path
print(f" Saved processed dataset: {csv_path}")

# 2. Save as pickle
pkl_path = 'preprocessed_dataset.pkl'
with open(pkl_path, 'wb') as f:
    pickle.dump(final_data, f)
output_files['pickle'] = pkl_path
print(f" Saved processed dataset: {pkl_path}")

# 3. Save train/val/test splits
splits_path = 'preprocessed_dataset_splits.pkl'
with open(splits_path, 'wb') as f:
    pickle.dump(data_splits, f)
output_files['splits'] = splits_path
print(f" Saved data splits: {splits_path}")

# 4. Save preprocessing objects
preprocessing_objects = {
    'scaler': scaler,
    'anomaly_detector': anomaly_detector,
    'feature_columns': feature_columns,
    'target_column': target_column,
    'original_shape': df_raw.shape,
    'final_shape': final_data.shape
}

objects_path = 'preprocessed_dataset_preprocessing_objects.pkl'
with open(objects_path, 'wb') as f:
    pickle.dump(preprocessing_objects, f)
output_files['objects'] = objects_path
print(f" Saved preprocessing objects: {objects_path}")

# Summary
print(f"\n Export Summary:")
print(f"  Original data shape: {df_raw.shape}")
print(f"  Final data shape: {final_data.shape}")
print(f"  Training samples: {len(X_train):,}")
print(f"  Validation samples: {len(X_val):,}")
print(f"  Test samples: {len(X_test):,}")
print(f"  Features: {len(feature_columns)}")
print(f"  Target classes: {sorted(final_data[target_column].unique())}")

print(f"\n🎉 Data preprocessing and analysis completed successfully!")
print(f"📁 Output files: {list(output_files.values())}")